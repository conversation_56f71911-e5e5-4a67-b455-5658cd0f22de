/* Upload Component Styles */

.upload-wrapper {
  width: 100%;
  max-width: var(--upload-dropzone-width);
}

.upload-container {
  .dropzone {
    width: var(--upload-dropzone-width);
    height: var(--upload-dropzone-height);
    background: var(--upload-dropzone-bg);
    box-shadow: var(--upload-dropzone-shadow);
    border-radius: var(--upload-dropzone-radius);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;

    // SVG positioning
    svg:first-of-type {
      position: absolute;
      width: 404px;
      height: 404px;
      left: calc(50% - 404px / 2);
      top: calc(50% - 404px / 2);
      z-index: 1;
      pointer-events: none;
    }

    svg:last-of-type {
      position: relative;
      width: 152px;
      height: 118px;
      margin-bottom: 24px;
      z-index: 1;
    }

    .upload-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      text-align: center;
    }

    .upload-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: var(--upload-title-color);
      margin: 0 0 4px 0;
    }

    .upload-subtitle {
      font-size: 14px;
      color: var(--upload-subtitle-color);
      margin: 0 0 15px 0;
      line-height: 1.4;
    }

    &:hover,
    &.drag-over {
      background-color: var(--upload-dropzone-hover-bg);
      border-color: var(--upload-dropzone-hover-border);
    }

    .files_holder {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      position: relative;
      
      .preview_item {
        .file_preview {
          display: flex;
          position: relative;
          min-width: 230px;
          max-width: 230px;
          flex-shrink: auto;
          background: #f1f1f1;
          border-radius: 12px;
          min-height: 180px;
          object-fit: cover;
          overflow: hidden;
          
          &.smaller {
            min-width: 140px;
            max-width: 140px;
            min-height: 120px;
          }
          
          .file_upload_progress {
            position: absolute;
            left: 0;
            height: 100%;
            display: flex;
            background: rgba(0, 0, 0, 0.2117647059);
            width: auto;
            transition: width 0.3s;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
          }
          
          img {
            border-radius: 12px;
          }
          
          .preview_btn {
            position: absolute;
            right: 10px;
            background: #fff;
            border-radius: 50px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-content: center;
            top: 10px;
          }
        }
      }
    }
  }

  input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }
}

.progress-section {
  width: 100%;
  max-width: var(--upload-dropzone-width);
  margin-top: 24px;
  
  .continue-button {
    width: 100%;
    justify-content: end;
    display: flex;
    
    button {
      width: 192px;
      height: 32px;
    }
  }
}

.error-message {
  color: var(--upload-error-color);
  font-size: 14px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--upload-error-bg);
  border: 1px solid var(--upload-error-border);
  border-radius: 8px;
}

.files-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.add-more-files {
  width: 100%;
  padding: 16px;
  border: 2px dashed #d0d5dd;
  border-radius: 8px;
  margin-top: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafbfc;

  &:hover {
    border-color: var(--upload-dropzone-hover-border);
    background: var(--upload-dropzone-hover-bg);
  }

  .add-more-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--upload-subtitle-color);
    font-size: 14px;
    font-weight: 500;

    .ff-icon {
      color: var(--upload-subtitle-color);
    }
  }

  &:hover .add-more-content {
    color: var(--upload-dropzone-hover-border);

    .ff-icon {
      color: var(--upload-dropzone-hover-border);
    }
  }
}

/* Upload Utility Classes */
.upload-grid-bg {
  background-color: var(--upload-grid-color);
}

.upload-primary-hover {
  &:hover {
    border-color: var(--upload-dropzone-hover-border);
    background-color: var(--upload-dropzone-hover-bg);
  }
}

.upload-shadow {
  box-shadow: var(--upload-dropzone-shadow);
}

/* Upload Size Variants */
.upload-sm {
  .dropzone {
    width: calc(var(--upload-dropzone-width) * 0.8);
    height: calc(var(--upload-dropzone-height) * 0.8);
  }
}

.upload-lg {
  .dropzone {
    width: calc(var(--upload-dropzone-width) * 1.2);
    height: calc(var(--upload-dropzone-height) * 1.2);
  }
}

/* Upload Theme Variants */
.upload-minimal {
  .dropzone {
    border: 2px dashed var(--upload-dropzone-border);
    background: transparent;
    box-shadow: none;
    
    &:hover,
    &.drag-over {
      border-color: var(--upload-dropzone-hover-border);
      background: var(--upload-dropzone-hover-bg);
    }
  }
}

.upload-compact {
  .dropzone {
    height: calc(var(--upload-dropzone-height) * 0.6);
    
    .upload-content {
      gap: 2px;
    }
    
    .upload-title {
      font-size: 14px;
      margin: 0 0 2px 0;
    }
    
    .upload-subtitle {
      font-size: 12px;
      margin: 0 0 8px 0;
    }
  }
}
