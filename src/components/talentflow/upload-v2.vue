<template>
  <ClientOnly>
    <div class="upload-wrapper">
      <!-- Upload Container - Hidden when files are present -->
      <div v-if="files.length === 0" class="upload-container">
        <p class="mb-1">
          Please upload your resume and it will be analyzed to enhance your
          profile.
        </p>

        <div
          class="dropzone"
          :class="{ 'drag-over': dragOver }"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
        >
          <input
            ref="fileInput"
            type="file"
            multiple
            @change="handleFileSelection"
            :accept="acceptTypes"
          />
          <svg
            width="504"
            height="283"
            viewBox="0 0 504 283"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask
              id="mask0_33_10885"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="12"
              y="-221"
              width="480"
              height="480"
            >
              <rect
                width="480"
                height="480"
                transform="translate(12 -221)"
                fill="url(#paint0_radial_33_10885)"
              />
            </mask>
            <g mask="url(#mask0_33_10885)">
              <g clip-path="url(#clip0_33_10885)">
                <g clip-path="url(#clip1_33_10885)">
                  <line
                    x1="12.5"
                    y1="-221"
                    x2="12.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="44.5"
                    y1="-221"
                    x2="44.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="76.5"
                    y1="-221"
                    x2="76.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="108.5"
                    y1="-221"
                    x2="108.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="140.5"
                    y1="-221"
                    x2="140.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="172.5"
                    y1="-221"
                    x2="172.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="204.5"
                    y1="-221"
                    x2="204.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="236.5"
                    y1="-221"
                    x2="236.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="268.5"
                    y1="-221"
                    x2="268.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="300.5"
                    y1="-221"
                    x2="300.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="332.5"
                    y1="-221"
                    x2="332.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="364.5"
                    y1="-221"
                    x2="364.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="396.5"
                    y1="-221"
                    x2="396.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="428.5"
                    y1="-221"
                    x2="428.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="460.5"
                    y1="-221"
                    x2="460.5"
                    y2="259"
                    stroke="#EAECF0"
                  />
                </g>
                <rect
                  x="12.5"
                  y="-220.5"
                  width="479"
                  height="479"
                  stroke="#EAECF0"
                />
                <g clip-path="url(#clip2_33_10885)">
                  <line x1="12" y1="2.5" x2="492" y2="2.5" stroke="#EAECF0" />
                  <line x1="12" y1="34.5" x2="492" y2="34.5" stroke="#EAECF0" />
                  <line x1="12" y1="66.5" x2="492" y2="66.5" stroke="#EAECF0" />
                  <line x1="12" y1="98.5" x2="492" y2="98.5" stroke="#EAECF0" />
                  <line
                    x1="12"
                    y1="130.5"
                    x2="492"
                    y2="130.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="162.5"
                    x2="492"
                    y2="162.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="194.5"
                    x2="492"
                    y2="194.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="226.5"
                    x2="492"
                    y2="226.5"
                    stroke="#EAECF0"
                  />
                  <line
                    x1="12"
                    y1="258.5"
                    x2="492"
                    y2="258.5"
                    stroke="#EAECF0"
                  />
                </g>
                <rect
                  x="12.5"
                  y="-220.5"
                  width="479"
                  height="479"
                  stroke="#EAECF0"
                />
              </g>
            </g>
            <defs>
              <radialGradient
                id="paint0_radial_33_10885"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(240 240) rotate(90) scale(240 240)"
              >
                <stop />
                <stop offset="1" stop-opacity="0" />
              </radialGradient>
              <clipPath id="clip0_33_10885">
                <rect
                  width="480"
                  height="480"
                  fill="white"
                  transform="translate(12 -221)"
                />
              </clipPath>
              <clipPath id="clip1_33_10885">
                <rect x="12" y="-221" width="480" height="480" fill="white" />
              </clipPath>
              <clipPath id="clip2_33_10885">
                <rect x="12" y="-221" width="480" height="480" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <svg
            width="152"
            height="120"
            viewBox="0 0 152 120"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="76" cy="52" r="52" fill="#EAECF0" />
            <g filter="url(#filter0_dd_33_10927)">
              <path
                d="M77.6 16C66.8273 16 57.2978 21.3233 51.4987 29.4829C49.605 29.0363 47.6301 28.8 45.6 28.8C31.4615 28.8 20 40.2615 20 54.4C20 68.5385 31.4615 80 45.6 80L109.6 80C121.971 80 132 69.9712 132 57.6C132 45.2288 121.971 35.2 109.6 35.2C108.721 35.2 107.854 35.2506 107.002 35.349C102.098 23.9677 90.7797 16 77.6 16Z"
                fill="#F9FAFB"
              />
              <ellipse
                cx="45.6"
                cy="54.3998"
                rx="25.6"
                ry="25.6"
                fill="url(#paint0_linear_33_10927)"
              />
              <circle
                cx="77.5996"
                cy="48"
                r="32"
                fill="url(#paint1_linear_33_10927)"
              />
              <ellipse
                cx="109.6"
                cy="57.6002"
                rx="22.4"
                ry="22.4"
                fill="url(#paint2_linear_33_10927)"
              />
            </g>
            <circle cx="21" cy="19" r="5" fill="#F2F4F7" />
            <circle cx="18" cy="109" r="7" fill="#F2F4F7" />
            <circle cx="145" cy="35" r="7" fill="#F2F4F7" />
            <circle cx="134" cy="8" r="4" fill="#F2F4F7" />
            <path
              d="M52 86C52 72.7452 62.7452 62 76 62C89.2548 62 100 72.7452 100 86C100 99.2548 89.2548 110 76 110C62.7452 110 52 99.2548 52 86Z"
              fill="#344054"
              fill-opacity="0.4"
            />
            <path
              d="M84.3904 92.3905C85.3658 91.8587 86.1363 91.0174 86.5803 89.9991C87.0244 88.9808 87.1167 87.8437 86.8427 86.7672C86.5686 85.6906 85.9439 84.736 85.0671 84.0539C84.1903 83.3719 83.1113 83.0012 82.0004 83.0005H80.7404C80.4378 81.8297 79.8736 80.7428 79.0904 79.8215C78.3072 78.9001 77.3253 78.1683 76.2185 77.6811C75.1118 77.1938 73.909 76.9638 72.7006 77.0084C71.4921 77.0529 70.3095 77.3708 69.2416 77.9381C68.1737 78.5055 67.2484 79.3076 66.5351 80.2841C65.8218 81.2605 65.3391 82.386 65.1234 83.5759C64.9077 84.7657 64.9646 85.989 65.2897 87.1537C65.6148 88.3185 66.1997 89.3943 67.0004 90.3005M76.0005 95.0005V86.0005M76.0005 86.0005L80.0005 89.9991M76.0005 86.0005L72.0005 89.9991"
              stroke="white"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <defs>
              <filter
                id="filter0_dd_33_10927"
                x="0"
                y="16"
                width="152"
                height="104"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="4"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect1_dropShadow_33_10927"
                />
                <feOffset dy="8" />
                <feGaussianBlur stdDeviation="4" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"
                />
                <feBlend
                  mode="normal"
                  in2="BackgroundImageFix"
                  result="effect1_dropShadow_33_10927"
                />
                <feColorMatrix
                  in="SourceAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                  result="hardAlpha"
                />
                <feMorphology
                  radius="4"
                  operator="erode"
                  in="SourceAlpha"
                  result="effect2_dropShadow_33_10927"
                />
                <feOffset dy="20" />
                <feGaussianBlur stdDeviation="12" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"
                />
                <feBlend
                  mode="normal"
                  in2="effect1_dropShadow_33_10927"
                  result="effect2_dropShadow_33_10927"
                />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="effect2_dropShadow_33_10927"
                  result="shape"
                />
              </filter>
              <linearGradient
                id="paint0_linear_33_10927"
                x1="25.9429"
                y1="37.4855"
                x2="71.2"
                y2="79.9998"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_33_10927"
                x1="53.0282"
                y1="26.8571"
                x2="109.6"
                y2="80"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_33_10927"
                x1="92.4002"
                y1="42.8002"
                x2="132"
                y2="80.0002"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#D0D5DD" />
                <stop offset="0.350715" stop-color="white" stop-opacity="0" />
              </linearGradient>
            </defs>
          </svg>

          <div class="upload-content">
            <h3 class="upload-title">Upload your resume</h3>
            <p class="upload-subtitle">Drag & drop a file here or...</p>

            <B_button @click="triggerFileInput" type="secondary" size="sm">
              Browse File
            </B_button>
          </div>
        </div>
      </div>

      <!-- Progress Bars Section - Shown when files are present -->
      <div v-if="files.length > 0" class="progress-section">
        <!-- Hidden file input for adding more files -->
        <input
          ref="additionalFileInput"
          type="file"
          multiple
          @change="handleFileSelection"
          :accept="acceptTypes"
          style="display: none"
        />

        <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
        <div class="files-list">
          <FileProgressBar
            v-for="(file, index) in files"
            :key="file.name"
            :file-name="file.name"
            :file-size="file.size"
            :progress="file.progress || 0"
            :is-uploading="file.progress !== undefined && file.progress < 100"
            left-icon="file"
            right-icon="trash"
            delete-icon="trash"
            @delete="removeFile(index)"
          />
        </div>
        <div class="continue-button">
          <B_button type="primary" size="lg" :disabled="isContinueDisabled"
            >Continue</B_button
          >
        </div>
      </div>

      <!-- Continue Button -->
      <template v-if="!uploadAutomatically">
        <B_button type="primary" :disabled="isContinueDisabled"
          >Continue</B_button
        >
      </template>
    </div>
  </ClientOnly>
</template>
<script setup lang="ts">
import B_button from "@components/talentflow/button.vue";
import FileProgressBar from "@components/talentflow/file-progress-bar.vue";
import { type IconName } from "@/components/ff/iconMap";

import { ref, computed } from "vue";

interface Props {
  acceptTypes: string;
  maxFileSize: number; // in bytes
  maxFiles: number;
  showProgress: boolean;
  uploadAction: string;
  uploadAutomatically: boolean;
  fileIcon?: IconName;
  deleteIcon?: IconName;
}

const props = defineProps<Props>();

const files = ref<
  (File & {
    preview?: string;
    icon?: string;
    progress?: number;
    cancel?: () => void;
    uploadParams?: { url: string; key?: string };
  })[]
>([]);
const dragOver = ref(false);
const errorMessage = ref<string>("");
const fileInput = ref<HTMLInputElement>();
const additionalFileInput = ref<HTMLInputElement>();

// Computed property to disable Continue button when no files are uploaded
const isContinueDisabled = computed(() => {
  return files.value.length === 0;
});

// Trigger file input click
function triggerFileInput() {
  fileInput.value?.click();
}

// Trigger additional file input click (for adding more files)
function triggerAdditionalFileInput() {
  additionalFileInput.value?.click();
}

// Handle drag over event
function handleDragOver(event: DragEvent) {
  event.preventDefault();
  dragOver.value = true;
}

// Handle drag leave event
function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;
}

// Handle file drop event
function handleDrop(event: DragEvent) {
  event.preventDefault();
  dragOver.value = false;

  const droppedFiles = event.dataTransfer?.files;
  if (droppedFiles) {
    processFiles(Array.from(droppedFiles));
  }
}

// Handle file selection from input
function handleFileSelection(event: Event) {
  const target = event.target as HTMLInputElement;
  const selectedFiles = target.files;
  if (selectedFiles) {
    processFiles(Array.from(selectedFiles));
  }
}

// Process selected/dropped files
function processFiles(fileList: File[]) {
  errorMessage.value = "";

  // Check if adding these files would exceed the max limit
  if (files.value.length + fileList.length > props.maxFiles) {
    errorMessage.value = `Maximum ${props.maxFiles} files allowed`;
    return;
  }

  for (const file of fileList) {
    // Check file size
    if (file.size > props.maxFileSize) {
      errorMessage.value = `File "${file.name}" is too large. Maximum size is ${
        props.maxFileSize / 1024 / 1024
      }MB`;
      continue;
    }

    // Check file type
    if (
      props.acceptTypes &&
      !props.acceptTypes
        .split(",")
        .some(
          (type) =>
            file.type.match(type.trim()) ||
            file.name.toLowerCase().endsWith(type.trim().replace("*", ""))
        )
    ) {
      errorMessage.value = `File "${file.name}" is not an accepted file type`;
      continue;
    }

    // Add file to the list
    const fileWithExtras = Object.assign(file, {
      preview: undefined as string | undefined,
      icon: undefined as string | undefined,
      progress: 0,
    });

    // Generate preview for images
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        fileWithExtras.preview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    } else {
      // Set icon for non-image files
      fileWithExtras.icon = getFileIcon(file.type);
    }

    files.value.push(fileWithExtras);

    // Start progress animation for the file
    startProgressAnimation(fileWithExtras);
  }
}

// Get file icon based on file type
function getFileIcon(fileType: string): string {
  if (fileType.includes("pdf")) return "/icons/pdf-icon.svg";
  if (fileType.includes("word") || fileType.includes("document"))
    return "/icons/doc-icon.svg";
  if (fileType.includes("text")) return "/icons/txt-icon.svg";
  return "/icons/file-icon.svg";
}

// Start progress animation for a file
function startProgressAnimation(file: any) {
  // Simulate upload progress
  const duration = 2000; // 2 seconds for demo
  const interval = 50; // Update every 50ms
  const steps = duration / interval;
  const increment = 100 / steps;

  let currentProgress = 0;

  const progressInterval = setInterval(() => {
    currentProgress += increment;

    if (currentProgress >= 100) {
      currentProgress = 100;
      clearInterval(progressInterval);
    }

    file.progress = Math.round(currentProgress);
  }, interval);
}

// Remove file from the list
function removeFile(index: number) {
  files.value.splice(index, 1);
}

// Progress and Removal Handlers
</script>

<style scoped lang="scss">
.upload-wrapper {
  width: 100%;
  max-width: var(--upload-dropzone-width);
}

.upload-container {
  .dropzone {
    width: var(--upload-dropzone-width);
    height: var(--upload-dropzone-height);
    background: var(--upload-dropzone-bg);
    box-shadow: var(--upload-dropzone-shadow);
    border-radius: var(--upload-dropzone-radius);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;

    // SVG positioning
    svg:first-of-type {
      position: absolute;
      width: 404px;
      height: 404px;
      left: calc(50% - 404px / 2);
      top: calc(50% - 404px / 2);
      z-index: 1;
      pointer-events: none;
    }

    svg:last-of-type {
      /* Illustration */
      position: relative;
      width: 152px;
      height: 118px;
      margin-bottom: 24px;
      z-index: 1;

      /* Inside auto layout */
    }

    .upload-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      text-align: center;
    }

    .upload-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: var(--upload-title-color);
      margin: 0 0 4px 0;
    }

    .upload-subtitle {
      font-size: 14px;
      color: var(--upload-subtitle-color);
      margin: 0 0 15px 0;
      line-height: 1.4;
    }

    .upload-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: #ffffff;
      border: 1px solid #d0d5dd;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #344054;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 16px;

      &:hover {
        background: #f9fafb;
        border-color: #98a2b3;
      }

      &:focus {
        outline: none;
        box-shadow: 0px 0px 0px 4px #f4ebff,
          0px 1px 2px 0px rgba(16, 24, 40, 0.05);
        border-color: #7f56d9;
      }

      svg {
        width: 20px;
        height: 20px;
        color: #667085;
      }
    }

    .upload-info {
      font-size: 14px;
      color: #667085;
      margin: 0 0 16px 0;
      line-height: 1.4;
    }

    .upload-formats {
      font-size: 12px;
      color: #667085;
      margin: 0;
      line-height: 1.4;
    }

    &:hover,
    &.drag-over {
      background-color: var(--upload-dropzone-hover-bg);
      border-color: var(--upload-dropzone-hover-border);
    }

    .files_holder {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      position: relative;
      .preview_item {
        .file_preview {
          display: flex;
          position: relative;
          min-width: 230px;
          max-width: 230px;
          flex-shrink: auto;
          background: #f1f1f1;
          border-radius: 12px;
          min-height: 180px;
          object-fit: cover;
          overflow: hidden;
          &.smaller {
            min-width: 140px;
            max-width: 140px;
            min-height: 120px;
          }
          .file_upload_progress {
            position: absolute;
            left: 0;
            height: 100%;
            display: flex;
            background: rgba(0, 0, 0, 0.2117647059);
            width: auto;
            transition: width 0.3s;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-weight: bold;
          }
          img {
            border-radius: 12px;
          }
          .preview_btn {
            position: absolute;
            right: 10px;
            background: #fff;
            border-radius: 50px;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-content: center;
            top: 10px;
          }
        }
      }
    }
  }

  input[type="file"] {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }
}

.progress-section {
  width: 100%;
  max-width: var(--upload-dropzone-width);
  margin-top: 24px;
  .continue-button {
    width: 100%;

    justify-content: end;
    display: flex;
    button {
      width: 192px;
      height: 32px;
    }
  }
}

.error-message {
  color: var(--upload-error-color);
  font-size: 14px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--upload-error-bg);
  border: 1px solid var(--upload-error-border);
  border-radius: 8px;
}

.files-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.add-more-files {
  width: 100%;
  padding: 16px;
  border: 2px dashed #d0d5dd;
  border-radius: 8px;
  margin-top: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fafbfc;

  &:hover {
    border-color: var(--upload-dropzone-hover-border);
    background: var(--upload-dropzone-hover-bg);
  }

  .add-more-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--upload-subtitle-color);
    font-size: 14px;
    font-weight: 500;

    .ff-icon {
      color: var(--upload-subtitle-color);
    }
  }

  &:hover .add-more-content {
    color: var(--upload-dropzone-hover-border);

    .ff-icon {
      color: var(--upload-dropzone-hover-border);
    }
  }
}
</style>
